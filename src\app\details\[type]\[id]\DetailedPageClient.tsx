'use client';

import { useState, useEffect } from 'react';
import useIsomorphicLayoutEffect from '@/hooks/useIsomorphicLayoutEffect';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Navbar } from '@/components/Navbar';
import SimpleFooter from '@/components/SimpleFooter';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Play, Plus, Check, Star, Calendar, Clock, Info, Share2, ArrowLeft, ArrowUp, Film, Award, Users, MessageCircle, ThumbsUp } from 'lucide-react';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { useReviews } from '@/contexts/ReviewContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import dynamic from 'next/dynamic';
import DetailRelatedContent from '@/components/DetailRelatedContent';
import ReviewTab from '@/components/ReviewTab';
import { ContentCardType } from '@/lib/content-utils';
import { motion } from 'framer-motion';
import { isBrowser, safeWindow, safeNavigator } from '@/lib/browser-utils';

const PersonImage = dynamic(() => import('@/components/PersonImage'), {
  ssr: false,
  loading: () => <div className="w-16 h-16 rounded-full bg-vista-dark-lighter animate-pulse"></div>
});

interface DetailedPageClientProps {
  params: {
    type: string;
    id: string;
  };
}

interface CastMember {
  id: string;
  name: string;
  character: string;
  profile_path: string | null;
  order: number;
  gender?: number;
  popularity?: number;
}

interface CrewMember {
  id: string;
  name: string;
  job: string;
  department: string;
  profile_path: string | null;
  gender?: number;
  popularity?: number;
}

interface DetailedContent {
  id: string;
  title: string;
  type: 'shows' | 'movies';
  description: string;
  releaseYear: number;
  ageRating: string;
  creators: string;
  starring: string[];
  genres: string[];
  seasons?: number;
  imagePath: string;
  bannerImage: string;
  duration: string;
  imdbId?: string;
  tmdbId?: string;
  director?: string;
  awards?: string;
  rated?: string;
  released?: string;
  metascore?: number;
  rating?: number;
  views?: number;
  videos?: TMDBVideoResult[];
  similar?: TMDBContentItem[];
  recommendations?: TMDBContentItem[];
  // Enhanced cast and crew data
  cast?: CastMember[];
  crew?: CrewMember[];
  credits?: {
    cast: CastMember[];
    crew: CrewMember[];
  };
}

// Define types for TMDB API response structures
interface TMDBCastMember {
  id: number;
  name: string;
  character?: string;
  profile_path: string | null;
  order: number;
  gender?: number;
  popularity?: number;
}

interface TMDBCrewMember {
  id: number;
  name: string;
  job: string;
  department: string;
  profile_path: string | null;
  gender?: number;
  popularity?: number;
}

interface TMDBVideoResult {
  id: string;
  key: string;
  name: string;
  site: string;
  size: number;
  type: string;
  official: boolean;
  published_at?: string;
  iso_639_1?: string;
  iso_3166_1?: string;
}

interface TMDBContentItem {
  id: number;
  title?: string;
  name?: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  vote_average: number;
  vote_count: number;
  release_date?: string;
  first_air_date?: string;
  genre_ids: number[];
  media_type?: 'movie' | 'tv';
  popularity?: number;
}

export default function DetailedPageClient({ params }: DetailedPageClientProps) {
  // Extract and validate params with improved logging and validation
  // console.log('[DetailedPageClient] Raw params received:', params); // Commented out due to linter errors

  // Check if params is undefined or null
  if (!params) {
    console.error('DetailedPageClient received undefined or null params');
  }

  // Safely extract type and id with improved debugging
  const type = params?.type || '';
  const id = params?.id || '';

  // console.log('[DetailedPageClient] Extracted params:', { type, id }); // Commented out due to linter errors
  // console.log('[DetailedPageClient] Param types:', { type: typeof type, id: typeof id }); // Commented out due to linter errors

  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | string | null>(null);
  const [data, setData] = useState<DetailedContent>({
    id: '',
    title: '',
    type: type === 'shows' ? 'shows' : 'movies',
    description: '',
    releaseYear: 0,
    ageRating: '',
    creators: '',
    starring: [],
    genres: [],
    imagePath: '',
    bannerImage: '',
    duration: '',
  });
  const [activeTab, setActiveTab] = useState('overview');
  const { addToWatchlist, removeFromWatchlist, isInWatchlist } = useWatchlist();
  const { stats: reviewStats, fetchReviews } = useReviews();
  const { user } = useAuth();
  const isInList = isInWatchlist(id);

  // Log params on component mount
  useEffect(() => {
    console.log('DetailedPageClient mounted with params:', { type, id });

    // Safely log window.location information using our utility functions
    const win = safeWindow();
    if (win) {
      console.log('Path information from window.location:', {
        pathname: win.location.pathname,
        href: win.location.href
      });
    }
  }, [type, id]);

  // We'll get review stats from the ReviewContext instead of making a separate API call
  // This will be updated when the ReviewTab component fetches reviews

  // Initialize review stats when component mounts
  useEffect(() => {
    if (id && type && fetchReviews) {
      const contentType = type === 'shows' ? 'show' : 'movie';
      // Fetch reviews to initialize stats
      fetchReviews(id, contentType);
    }
  }, [id, type, fetchReviews]);

  // Fetch content details
  useEffect(() => {
    console.log('DetailedPageClient fetch effect running with params:', { type, id });

    // Skip fetch if ID is invalid
    if (!id || id === 'undefined' || id === 'null' || id === '[object Object]' || id === 'unknown' || id === '') {
      console.error('Skipping fetch due to invalid ID:', id);
      setError('Failed to load content details - Invalid ID');
      setLoading(false);
      return;
    }

    const fetchContentDetails = async () => {
      try {
        setLoading(true);

        // Log the received params for debugging
        console.log('DetailedPageClient received params:', { type, id });

        // More robust ID validation
        if (!id || id === 'undefined' || id === 'null' || id === '[object Object]') {
          console.error('Invalid ID detected:', id);
          throw new Error(`Invalid content ID: ${id}`);
        }

        // Fetch content details from our API
        const contentType = type === 'shows' ? 'show' : 'movie';
        console.log(`Fetching content details for ${contentType} with ID: ${id}`);
        const response = await fetch(`/api/content?id=${id}&type=${contentType}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch content details: ${response.status}`);
        }

        const contentData = await response.json();
        console.log('Content data:', contentData);

        // Log credits data specifically
        if (contentData.credits) {
          console.log('Credits data:', {
            castCount: contentData.credits.cast?.length || 0,
            crewCount: contentData.credits.crew?.length || 0,
            castSample: contentData.credits.cast?.slice(0, 3).map((c: TMDBCastMember) => ({
              id: c.id,
              name: c.name,
              profile_path: c.profile_path
            })),
            crewSample: contentData.credits.crew?.slice(0, 3).map((c: TMDBCrewMember) => ({
              id: c.id,
              name: c.name,
              job: c.job,
              profile_path: c.profile_path
            }))
          });
        } else {
          console.warn('No credits data in content response');
        }



        // Process credits data if available
        let castData: CastMember[] = [];
        let crewData: CrewMember[] = [];

        if (contentData.credits) {
          console.log('Credits data available:', {
            castCount: contentData.credits.cast?.length || 0,
            crewCount: contentData.credits.crew?.length || 0
          });

          // Extract cast data (limit to top 20 for performance)
          castData = contentData.credits.cast?.slice(0, 20).map((cast: TMDBCastMember) => {
            console.log(`Cast member: ${cast.name}, has profile image: ${!!cast.profile_path}, ID: ${cast.id}`);
            return {
              id: cast.id.toString(),
              name: cast.name,
              character: cast.character || 'Unknown Role',
              profile_path: cast.profile_path,
              order: cast.order,
              gender: cast.gender,
              popularity: cast.popularity
            };
          }) || [];

          // Extract crew data (directors, writers, producers)
          crewData = contentData.credits.crew?.filter((crew: TMDBCrewMember) =>
            crew.job === 'Director' ||
            crew.job === 'Writer' ||
            crew.job === 'Producer' ||
            crew.job === 'Executive Producer' ||
            crew.department === 'Directing' ||
            crew.department === 'Writing'
          ).slice(0, 10).map((crew: TMDBCrewMember) => {
            console.log(`Crew member: ${crew.name}, job: ${crew.job}, has profile image: ${!!crew.profile_path}, ID: ${crew.id}`);
            return {
              id: crew.id.toString(),
              name: crew.name,
              job: crew.job,
              department: crew.department,
              profile_path: crew.profile_path,
              gender: crew.gender,
              popularity: crew.popularity
            };
          }) || [];

          console.log('Processed cast and crew data:', {
            processedCastCount: castData.length,
            processedCrewCount: crewData.length
          });
        } else {
          console.warn('No credits data available in content response');
        }

        // Map API data to our UI format
        setData({
          id: contentData.id.toString(),
          title: contentData.title,
          type: type === 'shows' ? 'shows' : 'movies',
          description: contentData.overview || '',
          releaseYear: contentData.year ? parseInt(contentData.year) : 0,
          ageRating: contentData.rated || (contentType === 'movie' ? 'PG-13' : 'TV-14'),
          creators: contentData.director || contentData.creator || '',
          starring: contentData.actors || [],
          genres: contentData.genres || [],
          seasons: contentData.seasons || 1,
          imagePath: contentData.posterPath || '',
          bannerImage: contentData.backdropPath || contentData.posterPath || '',
          duration: contentData.runtime ? `${contentData.runtime} min` : '45-60 min',
          imdbId: contentData.imdbId || '',
          tmdbId: contentData.tmdbId || '',
          director: contentData.director || '',
          awards: contentData.awards || '',
          rated: contentData.rated || '',
          released: contentData.released || '',
          metascore: contentData.metascore || 0,
          rating: contentData.rating || undefined,
          views: contentData.views || undefined,
          videos: contentData.videos,
          similar: contentData.similar?.results || [],
          recommendations: contentData.recommendations?.results || [],
          // Add enhanced cast and crew data
          cast: castData,
          crew: crewData,
          credits: contentData.credits
        });
      } catch (error) {
        console.error('Error fetching content details:', error);
        // Set error state
        setError(error instanceof Error ? error : new Error('Failed to load content details'));
        // Fallback to sample data using functional update
        setData(prevData => ({
          ...prevData,
          id,
          type: type === 'shows' ? 'shows' : 'movies',
        }));
      } finally {
        setLoading(false);
      }
    };

    fetchContentDetails();
  }, [id, type]);

  // Handle watchlist toggle
  const handleWatchlistToggle = () => {
    const contentItem: ContentCardType = {
      id: data.id,
      title: data.title,
      imagePath: data.imagePath,
      type: data.type,
      year: data.releaseYear.toString(),
      ageRating: data.ageRating,
      duration: data.duration,
    };

    if (isInList) {
      removeFromWatchlist(data.id);
      toast(`Removed "${data.title}" from My List`);
    } else {
      addToWatchlist(contentItem);
      toast(`Added "${data.title}" to My List`);
    }
  };

  // Handle play button click
  const handlePlayClick = () => {
    // For both movies and shows, redirect to the watch page with the proper ID
    const contentType = type === 'shows' ? 'show' : 'movie';

    // Determine the best ID to use for playback
    let contentId;

    // First try to use TMDb ID as it's most reliable for our API
    if (data.tmdbId && data.tmdbId.toString().trim() !== '') {
      console.log(`[DetailedPageClient] Using TMDb ID for playback: ${data.tmdbId}`);
      contentId = data.tmdbId;
    }
    // Then try IMDb ID if available
    else if (data.imdbId && data.imdbId.trim() !== '') {
      console.log(`[DetailedPageClient] Using IMDb ID for playback: ${data.imdbId}`);
      contentId = data.imdbId;
    }
    // Fallback to content ID
    else {
      console.log(`[DetailedPageClient] Using content ID for playback: ${data.id}`);
      contentId = data.id;
    }

    // Make sure contentId is a string
    contentId = contentId.toString();

    // Add additional info to URL to help debugging
    const watchUrl = `/watch/${contentId}?forcePlay=true&contentType=${contentType}&source=details&title=${encodeURIComponent(data.title)}`;
    console.log(`[DetailedPageClient] Navigating to: ${watchUrl}`);

    // Use router for navigation
    router.push(watchUrl);

    // Track analytics event
    try {
      // Example analytics tracking (implement based on your analytics system)
      console.log(`[Analytics] User started watching: ${data.title} (${contentType} ID: ${contentId})`);
    } catch (err) {
      console.error('[DetailedPageClient] Analytics error:', err);
    }
  };

  // Handle season selection and play
  const handleSeasonPlay = (seasonNumber: number) => {
    // For shows, redirect to the watch page with season information
    const contentType = 'show';

    // Determine the best ID to use for playback
    let contentId;

    // First try to use TMDb ID as it's most reliable for our API
    if (data.tmdbId && data.tmdbId.toString().trim() !== '') {
      console.log(`[DetailedPageClient] Using TMDb ID for playback: ${data.tmdbId}`);
      contentId = data.tmdbId;
    }
    // Then try IMDb ID if available
    else if (data.imdbId && data.imdbId.trim() !== '') {
      console.log(`[DetailedPageClient] Using IMDb ID for playback: ${data.imdbId}`);
      contentId = data.imdbId;
    }
    // Fallback to content ID
    else {
      console.log(`[DetailedPageClient] Using content ID for playback: ${data.id}`);
      contentId = data.id;
    }

    // Make sure contentId is a string
    contentId = contentId.toString();

    // Add season information to URL
    const watchUrl = `/watch/${contentId}?forcePlay=true&contentType=${contentType}&season=${seasonNumber}&episode=1&source=details&title=${encodeURIComponent(data.title)}`;
    console.log(`[DetailedPageClient] Navigating to season ${seasonNumber}: ${watchUrl}`);

    // Use router for navigation
    router.push(watchUrl);

    // Track analytics event
    try {
      // Example analytics tracking (implement based on your analytics system)
      console.log(`[Analytics] User started watching: ${data.title} Season ${seasonNumber} (${contentType} ID: ${contentId})`);
    } catch (err) {
      console.error('[DetailedPageClient] Analytics error:', err);
    }
  };



  // Handle share functionality
  const handleShare = () => {
    // Use our browser utility functions to safely access browser APIs
    const nav = safeNavigator();
    const win = safeWindow();

    if (!nav || !win) {
      console.warn('Share functionality is only available in browser environment');
      return;
    }

    // Check if the Web Share API is available
    if (nav.share) {
      nav.share({
        title: data.title,
        text: `Check out ${data.title} on StreamVista`,
        url: win.location.href,
      })
        .then(() => console.log('Successful share'))
        .catch((error) => console.log('Error sharing:', error));
    } else {
      // Fallback for browsers that don't support the Web Share API
      if (nav.clipboard) {
        nav.clipboard.writeText(win.location.href)
          .then(() => toast.success('Link copied to clipboard'))
          .catch(() => toast.error('Failed to copy link'));
      } else {
        // Final fallback if clipboard API is not available
        toast.info('Copy this URL to share: ' + win.location.href);
      }
    }
  };

  // State for showing all cast members
  const [showAllCast, setShowAllCast] = useState(false);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-vista-dark flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
      </div>
    );
  }

  // Error state or invalid ID
  if (error || !id || id === 'undefined' || id === 'null' || id === '[object Object]' || id === 'unknown' || id === '') {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light">
        <Navbar />
        <div className="container mx-auto px-4 py-16 flex flex-col items-center justify-center">
          <div className="bg-vista-dark-lighter rounded-lg p-8 max-w-md w-full text-center">
            <Info className="h-16 w-16 text-vista-light/30 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4">Content Not Found</h2>
            <p className="text-vista-light/70 mb-6">
              {error instanceof Error ? error.message : error || `Unable to load content details. Invalid ID: ${id}`}
            </p>
            <Button
              className="bg-vista-blue hover:bg-vista-blue/90 text-white"
              onClick={() => router.push('/')}
            >
              Return Home
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const isShow = data.type === 'shows';

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light overflow-x-hidden">
      <Navbar />

      {/* Enhanced back button with improved blur effect */}
      <div className="sticky top-0 z-40 bg-gradient-to-b from-vista-dark via-vista-dark/80 to-transparent py-3 xs:py-4 backdrop-blur-sm">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Button
            variant="ghost"
            className="text-vista-light/80 hover:text-vista-light hover:bg-vista-dark-lighter/80 active:bg-vista-dark-lighter backdrop-blur-md rounded-lg px-3 py-2 transition-all duration-200 shadow-sm shadow-black/10"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-1.5 xs:mr-2" />
            <span className="text-sm xs:text-base">Back</span>
          </Button>

        </div>
      </div>

      {/* Hero Section with Parallax Effect - Completely redesigned for mobile */}
      <div className="relative w-full h-[70vh] xs:h-[65vh] sm:h-[75vh] md:h-[80vh] lg:h-[85vh] overflow-hidden">
        {/* Dynamic Background with Multiple Layers */}
        {data.bannerImage && (
          <>
            {/* Main Background Image */}
            <div className="absolute inset-0">
              <Image
                src={data.bannerImage}
                alt={data.title}
                fill
                priority
                sizes="100vw"
                className="object-cover scale-105 transition-transform duration-[20s] ease-out hover:scale-110"
              />
            </div>
            
            {/* Animated Gradient Overlays */}
            <div className="absolute inset-0 bg-gradient-to-r from-vista-dark/95 via-vista-dark/80 to-vista-dark/60"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-vista-dark via-vista-dark/70 to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-vista-dark"></div>
            
            {/* Dynamic Animated Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 left-10 w-32 h-32 bg-vista-blue/20 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute top-32 right-20 w-48 h-48 bg-vista-accent/15 rounded-full blur-3xl animate-pulse delay-300"></div>
              <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-vista-blue/25 rounded-full blur-2xl animate-pulse delay-700"></div>
            </div>
            
            {/* Enhanced Vignette Effect */}
            <div className="absolute inset-0 bg-radial-gradient from-transparent via-transparent to-vista-dark/80"></div>
          </>
        )}

        {/* Content Overlay with enhanced layout */}
        <div className="container mx-auto px-4 absolute inset-0 flex flex-col justify-center z-30">
          <div className="max-w-4xl">
            {/* Category/Type Badge */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="mb-3 xs:mb-4"
            >
              <Badge 
                variant="outline" 
                className="bg-vista-blue/20 backdrop-blur-xl border-vista-blue/40 text-vista-blue font-semibold px-4 py-2 rounded-full text-sm shadow-lg hover:bg-vista-blue/30 transition-all duration-300"
              >
                {isShow ? 'TV Series' : 'Movie'}
              </Badge>
            </motion.div>

            {/* Enhanced Title with Better Typography */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black text-white mb-4 xs:mb-6 leading-tight tracking-tight"
              style={{
                textShadow: '0 4px 20px rgba(0, 0, 0, 0.8), 0 8px 40px rgba(0, 0, 0, 0.4)',
                background: 'linear-gradient(135deg, #ffffff 0%, #e6f3ff 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              {data.title}
            </motion.h1>

            {/* Enhanced Description */}
            {data.description && (
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-vista-light/90 text-base sm:text-lg md:text-xl max-w-2xl mb-6 leading-relaxed font-medium"
                style={{ textShadow: '0 2px 10px rgba(0, 0, 0, 0.7)' }}
              >
                {data.description.length > 200 ? `${data.description.slice(0, 200)}...` : data.description}
              </motion.p>
            )}

            {/* Enhanced Metadata Cards Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-6 xs:mb-8"
            >
              {/* Release Year Card */}
              {data.releaseYear > 0 && (
                <div className="bg-black/40 backdrop-blur-xl border border-white/20 rounded-2xl p-4 text-center shadow-2xl hover:bg-black/50 hover:border-white/30 transition-all duration-300 hover:scale-105">
                  <Calendar className="h-5 w-5 text-vista-blue mx-auto mb-2" />
                  <div className="text-white font-bold text-lg">{data.releaseYear}</div>
                  <div className="text-white/70 text-xs uppercase tracking-wider">Year</div>
                </div>
              )}

                             {/* Rating Card */}
               {(data.metascore && data.metascore > 0 || data.rating) && (
                 <div className="bg-black/40 backdrop-blur-xl border border-white/20 rounded-2xl p-4 text-center shadow-2xl hover:bg-black/50 hover:border-white/30 transition-all duration-300 hover:scale-105">
                   <Star className="h-5 w-5 text-yellow-400 mx-auto mb-2" />
                   <div className="text-white font-bold text-lg">
                     {data.metascore && data.metascore > 0 ? `${data.metascore}%` : `${data.rating?.toFixed(1)}/10`}
                   </div>
                   <div className="text-white/70 text-xs uppercase tracking-wider">Rating</div>
                 </div>
               )}

              {/* Duration Card */}
              {data.duration && (
                <div className="bg-black/40 backdrop-blur-xl border border-white/20 rounded-2xl p-4 text-center shadow-2xl hover:bg-black/50 hover:border-white/30 transition-all duration-300 hover:scale-105">
                  <Clock className="h-5 w-5 text-vista-accent mx-auto mb-2" />
                  <div className="text-white font-bold text-lg">{data.duration}</div>
                  <div className="text-white/70 text-xs uppercase tracking-wider">Runtime</div>
                </div>
              )}

              {/* Age Rating Card */}
              {data.ageRating && (
                <div className="bg-black/40 backdrop-blur-xl border border-white/20 rounded-2xl p-4 text-center shadow-2xl hover:bg-black/50 hover:border-white/30 transition-all duration-300 hover:scale-105">
                  <Info className="h-5 w-5 text-vista-light mx-auto mb-2" />
                  <div className="text-white font-bold text-lg">{data.ageRating}</div>
                  <div className="text-white/70 text-xs uppercase tracking-wider">Rated</div>
                </div>
              )}
            </motion.div>

            {/* Genre Pills */}
            {data.genres.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="flex flex-wrap gap-2 mb-6 xs:mb-8"
              >
                {data.genres.slice(0, 5).map((genre, index) => (
                  <Badge
                    key={genre}
                    variant="secondary"
                    className="bg-vista-blue/20 backdrop-blur-xl border-vista-blue/40 text-vista-blue hover:bg-vista-blue/30 px-4 py-2 rounded-full text-sm font-medium shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    {genre}
                  </Badge>
                ))}
              </motion.div>
            )}

            {/* Enhanced Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="flex flex-col sm:flex-row gap-4 sm:gap-6"
            >
              {/* Primary Play Button */}
              <Button
                className="group relative overflow-hidden bg-gradient-to-r from-vista-blue to-vista-blue/90 hover:from-vista-blue/90 hover:to-vista-blue text-white font-bold text-lg px-8 py-4 rounded-2xl shadow-2xl shadow-vista-blue/40 hover:shadow-vista-blue/60 transition-all duration-500 hover:scale-105 border-2 border-vista-blue/30 hover:border-vista-blue/50"
                onClick={handlePlayClick}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <Play className="h-6 w-6 mr-3 relative z-10" />
                <span className="relative z-10">{isShow ? 'Watch S1:E1' : 'Watch Now'}</span>
              </Button>

              {/* Secondary Buttons */}
              <div className="flex gap-4">
                <Button
                  variant="outline"
                  className="group relative overflow-hidden bg-black/40 backdrop-blur-xl hover:bg-black/60 text-white border-2 border-white/30 hover:border-white/50 font-semibold px-6 py-4 rounded-2xl shadow-xl transition-all duration-500 hover:scale-105"
                  onClick={handleWatchlistToggle}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  {isInList ? (
                    <>
                      <Check className="h-5 w-5 mr-2 text-vista-accent relative z-10" />
                      <span className="relative z-10">In My List</span>
                    </>
                  ) : (
                    <>
                      <Plus className="h-5 w-5 mr-2 relative z-10" />
                      <span className="relative z-10">My List</span>
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  className="group relative overflow-hidden bg-black/40 backdrop-blur-xl hover:bg-black/60 text-white border-2 border-white/30 hover:border-white/50 font-semibold px-6 py-4 rounded-2xl shadow-xl transition-all duration-500 hover:scale-105"
                  onClick={handleShare}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <Share2 className="h-5 w-5 mr-2 relative z-10" />
                  <span className="relative z-10">Share</span>
                </Button>


              </div>
            </motion.div>

            {/* Quick Stats Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.9 }}
              className="mt-8 xs:mt-12 flex flex-wrap items-center gap-6 text-white/80"
            >
              {data.views && data.views > 0 && (
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-vista-accent" />
                  <span className="text-sm font-medium">{data.views.toLocaleString()} viewers</span>
                </div>
              )}
              
              {reviewStats.reviewCount > 0 && (
                <div className="flex items-center gap-2 cursor-pointer hover:text-white transition-colors duration-200" onClick={() => setActiveTab('reviews')}>
                  <MessageCircle className="h-4 w-4 text-vista-blue" />
                  <span className="text-sm font-medium">{reviewStats.reviewCount} reviews</span>
                </div>
              )}
              
              {data.awards && (
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium">Award Winner</span>
                </div>
              )}
            </motion.div>
          </div>
        </div>


      </div>



      {/* Main Content with Modern Glass Effect Sections - Mobile optimized */}
      <div className="relative z-30 pt-2 xs:pt-4 sm:pt-8">
        <div className="container mx-auto px-4 pb-16">
          {/* User Engagement Stats Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-4 xs:mb-6 sm:mb-8 bg-gradient-to-r from-vista-dark-lighter/80 to-vista-dark/80 backdrop-blur-xl rounded-xl sm:rounded-2xl overflow-hidden shadow-xl border border-vista-light/10 p-2 xs:p-3 sm:p-4 transform transition-all duration-300 hover:shadow-2xl hover:border-vista-light/20"
          >
            <div className="flex flex-nowrap md:grid md:grid-cols-4 gap-2 sm:gap-4 overflow-x-auto pb-1 scrollbar-hide -mx-1 px-1">
              <div className="flex flex-col items-center justify-center p-2 sm:p-3 rounded-lg sm:rounded-xl bg-vista-dark/40 border border-vista-light/5 min-w-[100px] flex-shrink-0 md:flex-shrink transform transition-all duration-300 hover:bg-vista-dark/60 hover:border-vista-light/10 hover:scale-[1.02]">
                <div className="flex items-center mb-1">
                  <ThumbsUp className="h-4 w-4 text-vista-blue mr-2" />
                  <span className="text-vista-light/90 text-sm font-medium">
                    {data.metascore
                      ? `${data.metascore}%`
                      : data.rating
                        ? `${Math.round(data.rating * 10)}%`
                        : 'New'}
                  </span>
                </div>
                <span className="text-xs text-vista-light/60">Match</span>
              </div>

              <div className="flex flex-col items-center justify-center p-2 sm:p-3 rounded-lg sm:rounded-xl bg-vista-dark/40 border border-vista-light/5 min-w-[100px] flex-shrink-0 md:flex-shrink transform transition-all duration-300 hover:bg-vista-dark/60 hover:border-vista-light/10 hover:scale-[1.02]">
                <div className="flex items-center mb-1">
                  <Award className="h-4 w-4 text-vista-accent mr-2" />
                  <span className="text-vista-light/90 text-sm font-medium">
                    {reviewStats.averageRating > 0
                      ? `${reviewStats.averageRating.toFixed(1)}/5`
                      : data.rating
                        ? `${data.rating.toFixed(1)}/10`
                        : 'N/A'}
                  </span>
                </div>
                <span className="text-xs text-vista-light/60">Rating</span>
              </div>

              <div className="flex flex-col items-center justify-center p-2 sm:p-3 rounded-lg sm:rounded-xl bg-vista-dark/40 border border-vista-light/5 min-w-[100px] flex-shrink-0 md:flex-shrink transform transition-all duration-300 hover:bg-vista-dark/60 hover:border-vista-light/10 hover:scale-[1.02]">
                <div className="flex items-center mb-1">
                  <Users className="h-4 w-4 text-vista-accent-alt mr-2" />
                  <span className="text-vista-light/90 text-sm font-medium">
                    {data.views && data.views > 0 ? `${data.views.toLocaleString()}` : 'New'}
                  </span>
                </div>
                <span className="text-xs text-vista-light/60">Viewers</span>
              </div>

              <div className="flex flex-col items-center justify-center p-2 sm:p-3 rounded-lg sm:rounded-xl bg-vista-dark/40 border border-vista-light/5 min-w-[100px] flex-shrink-0 md:flex-shrink transform transition-all duration-300 hover:bg-vista-dark/60 hover:border-vista-light/10 hover:scale-[1.02]"
                onClick={() => setActiveTab('reviews')}
                style={{ cursor: 'pointer' }}
              >
                <div className="flex items-center mb-1">
                  <MessageCircle className="h-4 w-4 text-vista-light/70 mr-2" />
                  <span className="text-vista-light/90 text-sm font-medium">Reviews</span>
                </div>
                <span className="text-xs text-vista-light/60">
                  {reviewStats.reviewCount > 0
                    ? reviewStats.reviewCount > 999
                      ? `${(reviewStats.reviewCount / 1000).toFixed(1)}K`
                      : reviewStats.reviewCount
                    : 'Add yours'}
                </span>
              </div>
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
            {/* Left Column - Trailer and Details */}
            <div className="lg:col-span-2">


              {/* Tabs for different content sections with Modern Glass Morphism */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                className="mb-6 sm:mb-8 bg-gradient-to-br from-vista-dark-lighter/70 to-vista-dark/70 backdrop-blur-xl rounded-xl sm:rounded-2xl overflow-hidden shadow-xl border border-vista-light/10"
              >
                <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="p-4 sm:p-6">
                  <TabsList className="bg-vista-dark/60 backdrop-blur-sm rounded-xl p-1 mb-4 sm:mb-6 border border-vista-light/10 shadow-inner w-full overflow-x-auto flex-nowrap">
                    <TabsTrigger value="overview" className="rounded-lg text-xs sm:text-sm whitespace-nowrap data-[state=active]:bg-gradient-to-r data-[state=active]:from-vista-blue data-[state=active]:to-vista-blue/80 data-[state=active]:text-white data-[state=active]:shadow-md">Overview</TabsTrigger>
                    <TabsTrigger value="details" className="rounded-lg text-xs sm:text-sm whitespace-nowrap data-[state=active]:bg-gradient-to-r data-[state=active]:from-vista-blue data-[state=active]:to-vista-blue/80 data-[state=active]:text-white data-[state=active]:shadow-md">Details</TabsTrigger>
                    {isShow && <TabsTrigger value="seasons" className="rounded-lg text-xs sm:text-sm whitespace-nowrap data-[state=active]:bg-gradient-to-r data-[state=active]:from-vista-blue data-[state=active]:to-vista-blue/80 data-[state=active]:text-white data-[state=active]:shadow-md">Seasons</TabsTrigger>}
                    <TabsTrigger value="reviews" className="rounded-lg text-xs sm:text-sm whitespace-nowrap data-[state=active]:bg-gradient-to-r data-[state=active]:from-vista-blue data-[state=active]:to-vista-blue/80 data-[state=active]:text-white data-[state=active]:shadow-md">Reviews</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="pt-4 animate-in fade-in-50 duration-300">
                    <h2 className="text-2xl font-bold mb-4 flex items-center">
                      <Info className="mr-2 h-5 w-5 text-vista-blue" />
                      Overview
                    </h2>
                    <p className="text-vista-light/90 mb-6 leading-relaxed">
                      {data.description || 'No overview available.'}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                      {data.starring.length > 0 && (
                        <div className="bg-vista-dark/40 backdrop-blur-sm rounded-lg sm:rounded-xl p-4 sm:p-5 border border-vista-light/5">
                          <h3 className="text-lg font-semibold mb-3 flex items-center">
                            <Star className="h-4 w-4 mr-2 text-vista-blue" />
                            Starring
                          </h3>
                          <ul className="space-y-2 text-vista-light/80">
                            {data.starring.map((actor, index) => (
                              <li key={index} className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-vista-blue/50 mr-2"></div>
                                {actor}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      <div className="bg-vista-dark/40 backdrop-blur-sm rounded-lg sm:rounded-xl p-4 sm:p-5 border border-vista-light/5">
                        {data.creators && (
                          <div className="mb-6">
                            <h3 className="text-lg font-semibold mb-3 flex items-center group">
                              <Calendar className="h-4 w-4 mr-2 text-vista-blue group-hover:text-vista-blue/80 transition-colors duration-300" />
                              {isShow ? 'Created by' : 'Director'}
                            </h3>
                            <p className="text-vista-light/80 hover:text-vista-light transition-colors duration-300">{data.creators}</p>
                          </div>
                        )}

                        {data.released && (
                          <div className="mb-6">
                            <h3 className="text-lg font-semibold mb-3 flex items-center group">
                              <Calendar className="h-4 w-4 mr-2 text-vista-blue group-hover:text-vista-blue/80 transition-colors duration-300" />
                              Release Date
                            </h3>
                            <p className="text-vista-light/80 hover:text-vista-light transition-colors duration-300">{data.released}</p>
                          </div>
                        )}

                        {data.metascore && data.metascore > 0 && (
                          <div className="mb-6">
                            <h3 className="text-lg font-semibold mb-3 flex items-center group">
                              <Star className="h-4 w-4 mr-2 text-vista-blue group-hover:text-vista-blue/80 transition-colors duration-300" />
                              Metascore
                            </h3>
                            <div className="flex items-center">
                              <div className={`w-10 h-10 flex items-center justify-center rounded-md mr-3 shadow-md transition-all duration-300 hover:scale-110 ${data.metascore >= 70 ? 'bg-green-600 hover:bg-green-500' : data.metascore >= 50 ? 'bg-yellow-600 hover:bg-yellow-500' : 'bg-red-600 hover:bg-red-500'}`}>
                                <span className="text-white font-bold text-sm">{data.metascore}</span>
                              </div>
                              <p className="text-vista-light/80 hover:text-vista-light transition-colors duration-300">{data.metascore >= 70 ? 'Excellent' : data.metascore >= 50 ? 'Good' : 'Poor'}</p>
                            </div>
                          </div>
                        )}

                        {data.awards && (
                          <div className="mb-6">
                            <h3 className="text-lg font-semibold mb-3 flex items-center">
                              <Award className="h-4 w-4 mr-2 text-vista-blue" />
                              Awards
                            </h3>
                            <p className="text-vista-light/80">{data.awards}</p>
                          </div>
                        )}

                        {data.imdbId && (
                          <div>
                            <h3 className="text-lg font-semibold mb-3 flex items-center">
                              <Film className="h-4 w-4 mr-2 text-vista-blue" />
                              IMDb
                            </h3>
                            <a
                              href={`https://www.imdb.com/title/${data.imdbId}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-vista-blue hover:text-vista-blue/80 transition-colors flex items-center"
                            >
                              View on IMDb
                              <ArrowLeft className="h-3 w-3 ml-1 rotate-[135deg]" />
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="details" className="pt-4 animate-in fade-in-50 duration-300">
                    <h2 className="text-2xl font-bold mb-4 flex items-center group">
                      <Info className="mr-2 h-5 w-5 text-vista-blue group-hover:text-vista-blue/80 transition-colors duration-300" />
                      Details
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                      <div className="bg-vista-dark/40 backdrop-blur-sm rounded-lg sm:rounded-xl p-4 sm:p-5 border border-vista-light/5 space-y-3 sm:space-y-4 transform transition-all duration-300 hover:bg-vista-dark/50 hover:border-vista-light/10 hover:shadow-lg">
                        {data.releaseYear > 0 && (
                          <div>
                            <h3 className="text-sm font-medium mb-1 text-vista-light/60 group-hover:text-vista-light/70 transition-colors duration-300">Release Year</h3>
                            <p className="text-vista-light text-lg hover:text-vista-blue/90 transition-colors duration-300">{data.releaseYear}</p>
                          </div>
                        )}

                        {data.ageRating && (
                          <div>
                            <h3 className="text-sm font-medium mb-1 text-vista-light/60">Age Rating</h3>
                            <p className="text-vista-light text-lg">{data.ageRating}</p>
                          </div>
                        )}

                        {data.duration && (
                          <div>
                            <h3 className="text-sm font-medium mb-1 text-vista-light/60">Duration</h3>
                            <p className="text-vista-light text-lg">{data.duration}</p>
                          </div>
                        )}
                      </div>

                      <div className="bg-vista-dark/40 backdrop-blur-sm rounded-lg sm:rounded-xl p-4 sm:p-5 border border-vista-light/5 space-y-3 sm:space-y-4">
                        {data.genres.length > 0 && (
                          <div>
                            <h3 className="text-sm font-medium mb-2 text-vista-light/60">Genres</h3>
                            <div className="flex flex-wrap gap-2">
                              {data.genres.map((genre, index) => (
                                <Badge key={index} variant="secondary" className="bg-vista-blue/20 text-vista-light border border-vista-blue/30">
                                  {genre}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {isShow && data.seasons && (
                          <div>
                            <h3 className="text-sm font-medium mb-1 text-vista-light/60">Seasons</h3>
                            <p className="text-vista-light text-lg">{data.seasons}</p>
                          </div>
                        )}

                        {data.imdbId && (
                          <div>
                            <h3 className="text-sm font-medium mb-1 text-vista-light/60">IMDb ID</h3>
                            <p className="text-vista-light text-lg">{data.imdbId}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </TabsContent>

                  {isShow && (
                    <TabsContent value="seasons" className="pt-4 animate-in fade-in-50 duration-300">
                      <h2 className="text-2xl font-bold mb-4 flex items-center">
                        <Calendar className="mr-2 h-5 w-5 text-vista-blue" />
                        Seasons
                      </h2>
                      <div className="bg-gradient-to-br from-vista-dark/60 to-vista-dark-lighter/40 backdrop-blur-sm rounded-lg sm:rounded-xl p-4 sm:p-6 border border-vista-light/10 shadow-inner">
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
                          <div>
                            <div className="flex items-center mb-2">
                              <Badge variant="outline" className="bg-vista-blue/10 border-vista-blue/30 text-vista-light mr-2">
                                {data.seasons} {data.seasons === 1 ? 'Season' : 'Seasons'}
                              </Badge>
                              <Badge variant="outline" className="bg-vista-accent/10 border-vista-accent/30 text-vista-light">
                                {isShow ? 'TV Series' : 'Movie'}
                              </Badge>
                            </div>
                            <p className="text-vista-light/80 mb-2">
                              This show has {data.seasons} {data.seasons === 1 ? 'season' : 'seasons'} available to stream.
                            </p>
                            <p className="text-vista-light/60 text-sm">
                              Start watching from Season 1, Episode 1
                            </p>
                          </div>
                          <Button
                            className="bg-gradient-to-r from-vista-blue to-vista-blue/90 hover:from-vista-blue/90 hover:to-vista-blue text-white gap-2 rounded-full px-5 py-5 shadow-lg shadow-vista-blue/20 transition-all duration-300 hover:shadow-vista-blue/40 hover:scale-105"
                            onClick={handlePlayClick}
                          >
                            <Play className="h-4 w-4" />
                            Start Watching
                          </Button>
                        </div>

                        {/* Season pills with improved design */}
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 sm:gap-3">
                          {Array.from({ length: data.seasons || 0 }).map((_, index) => (
                            <Button
                              key={index}
                              variant="outline"
                              onClick={() => handleSeasonPlay(index + 1)}
                              className="bg-vista-dark/60 hover:bg-vista-blue/20 px-4 py-3 rounded-xl border border-vista-light/10 text-vista-light transition-all duration-300 hover:border-vista-blue/30 flex items-center gap-2 shadow-sm hover:shadow-md"
                            >
                              <Calendar className="h-4 w-4 text-vista-blue/80" />
                              Season {index + 1}
                              <Play className="h-3 w-3 ml-auto text-vista-light/70" />
                            </Button>
                          ))}
                        </div>
                      </div>
                    </TabsContent>
                  )}

                  {/* Reviews Tab */}
                  <TabsContent value="reviews" className="pt-4 animate-in fade-in-50 duration-300">
                    <h2 className="text-2xl font-bold mb-4 flex items-center group">
                      <MessageCircle className="mr-2 h-5 w-5 text-vista-blue group-hover:text-vista-blue/80 transition-colors duration-300" />
                      User Reviews
                    </h2>
                    <ReviewTab
                      contentId={data.id}
                      contentType={data.type === 'shows' ? 'show' : 'movie'}
                      userId={user?.id}
                      username={user?.name}
                    />
                  </TabsContent>
                </Tabs>
              </motion.div>
            </div>

            {/* Right Column - Poster and Additional Info - Improved for mobile */}
            <div className="mt-6 lg:mt-0">
              {/* Poster with enhanced hover effect */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="mb-6 group"
              >
                <div className="relative aspect-[2/3] max-w-[220px] sm:max-w-[300px] mx-auto lg:mx-0 rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl transition-all duration-500 transform group-hover:shadow-vista-blue/30 border border-vista-light/10">
                  <Image
                    src={data.imagePath}
                    alt={data.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-vista-dark/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Hover Action Buttons */}
                  <div className="absolute inset-x-0 bottom-0 p-4 flex justify-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                    <Button
                      size="sm"
                      className="bg-vista-blue/90 hover:bg-vista-blue text-white rounded-full w-10 h-10 p-0 shadow-lg"
                      onClick={handlePlayClick}
                    >
                      <Play className="h-5 w-5" />
                    </Button>
                    <Button
                      size="sm"
                      className="bg-vista-dark/80 hover:bg-vista-dark-lighter/90 text-white rounded-full w-10 h-10 p-0 shadow-lg"
                      onClick={handleWatchlistToggle}
                    >
                      {isInList ? <Check className="h-5 w-5" /> : <Plus className="h-5 w-5" />}
                    </Button>
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Cast & Crew Section with Modern Design */}
              {((data.cast && data.cast.length > 0) || (data.crew && data.crew.length > 0) || data.starring.length > 0) && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="bg-gradient-to-br from-vista-dark-lighter/70 to-vista-dark/70 backdrop-blur-xl rounded-xl sm:rounded-2xl p-4 sm:p-6 shadow-xl border border-vista-light/10"
                >
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Star className="h-4 w-4 mr-2 text-vista-blue" />
                    Cast & Crew
                  </h3>

                  {/* Directors/Creators Section */}
                  {((data.crew && data.crew.some(c => c.job === 'Director' || c.job === 'Creator')) || data.creators) && (
                    <div className="mb-6">
                      <p className="text-vista-light/70 text-xs font-medium mb-3 uppercase tracking-wider">{isShow ? 'Created by' : 'Director'}</p>
                      <div className="space-y-3">
                        {data.crew && data.crew
                          .filter(c => c.job === 'Director' || c.job === 'Creator')
                          .map((director) => (
                            <div key={director.id} className="flex items-center bg-vista-dark/40 rounded-lg p-3 border border-vista-light/5 transition-all duration-300 hover:border-vista-blue/20 hover:bg-vista-dark/60 transform hover:scale-[1.02]">
                              <PersonImage
                                id={director.id}
                                name={director.name}
                                profilePath={director.profile_path}
                                gender={director.gender}
                                className="mr-3"
                                priority={true}
                                size="lg"
                              />
                              <div>
                                <p className="text-vista-light font-medium text-sm leading-tight">{director.name}</p>
                                <p className="text-vista-light/60 text-xs">{director.job}</p>
                              </div>
                            </div>
                          ))}

                        {/* Fallback to creators string if no crew data */}
                        {(!data.crew || data.crew.filter(c => c.job === 'Director' || c.job === 'Creator').length === 0) && data.creators && (
                          <div className="flex items-center bg-vista-dark/40 rounded-lg p-3 border border-vista-light/5">
                            <div className="w-10 h-10 rounded-full bg-vista-blue/20 flex items-center justify-center mr-3 text-sm font-medium flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm">
                              {data.creators.charAt(0).toUpperCase()}
                            </div>
                            <div>
                              <p className="text-vista-light font-medium text-sm leading-tight">{data.creators}</p>
                              <p className="text-vista-light/60 text-xs">{isShow ? 'Creator' : 'Director'}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Cast Section with Images */}
                  {((data.cast && data.cast.length > 0) || data.starring.length > 0) && (
                    <div className="mb-6">
                      <p className="text-vista-light/70 text-xs font-medium mb-3 uppercase tracking-wider">Starring</p>
                      <div className="grid grid-cols-1 gap-3">
                        {/* Use enhanced cast data if available */}
                        {data.cast && data.cast.length > 0 ? (
                          data.cast.slice(0, showAllCast ? data.cast.length : 4).map((actor, index) => (
                            <div key={actor.id} className="flex items-center bg-vista-dark/40 rounded-lg p-3 border border-vista-light/5 transition-all duration-300 hover:border-vista-blue/20 hover:bg-vista-dark/60 transform hover:scale-[1.02]">
                              <PersonImage
                                id={actor.id}
                                name={actor.name}
                                profilePath={actor.profile_path}
                                gender={actor.gender}
                                className="mr-3"
                                priority={index < 2}
                                size={index < 2 ? "lg" : "md"}
                              />
                              <div>
                                <p className="text-vista-light font-medium text-sm leading-tight">{actor.name}</p>
                                <p className="text-vista-light/60 text-xs line-clamp-1">{actor.character}</p>
                              </div>
                            </div>
                          ))
                        ) : (
                          // Fallback to basic starring array if no cast data
                          data.starring.slice(0, showAllCast ? data.starring.length : 4).map((actor, index) => (
                            <div key={index} className="flex items-center bg-vista-dark/40 rounded-lg p-3 border border-vista-light/5">
                              <div className="w-10 h-10 rounded-full bg-vista-blue/20 flex items-center justify-center mr-3 text-sm font-medium flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm">
                                {actor.charAt(0).toUpperCase()}
                              </div>
                              <span className="text-vista-light text-sm">{actor}</span>
                            </div>
                          ))
                        )}
                      </div>

                      {/* Show/Hide buttons - Refactored Logic */}
                      {(() => { // IIFE to calculate count
                        const totalCastCount = data.cast?.length ?? data.starring.length;
                        if (totalCastCount > 4) { // Check if button is needed
                          if (!showAllCast) {
                            // Show 'View All' button
                            return (
                              <Button
                                variant="link"
                                className="w-full mt-3 text-vista-blue hover:text-vista-blue/80 text-sm p-0 h-auto justify-start"
                                onClick={() => setShowAllCast(true)}
                              >
                                View All {totalCastCount} Cast Members
                              </Button>
                            );
                          } else {
                            // Show 'Show Less' button
                            return (
                              <Button
                                variant="link"
                                className="w-full mt-3 text-vista-blue hover:text-vista-blue/80 text-sm p-0 h-auto justify-start"
                                onClick={() => setShowAllCast(false)}
                              >
                                Show Less
                              </Button>
                            );
                          }
                        }
                        return null; // No button needed if count <= 4
                      })()}
                    </div>
                  )}

                  {/* Writers/Producers Section - Exclude directors already shown above */}
                  {data.crew && data.crew.filter(c =>
                    // Exclude directors and creators (already shown in the director section)
                    c.job !== 'Director' &&
                    c.job !== 'Creator' &&
                    // Also exclude any crew member who is already shown as a director
                    !data.crew?.some(director =>
                      (director.job === 'Director' || director.job === 'Creator') &&
                      director.id === c.id
                    )
                  ).length > 0 && (
                    <div className="pt-6 border-t border-vista-light/10">
                      <p className="text-vista-light/70 text-xs font-medium mb-3 uppercase tracking-wider">More Crew</p>
                      <div className="grid grid-cols-1 gap-3">
                        {data.crew
                          .filter(c =>
                            // Exclude directors and creators (already shown in the director section)
                            c.job !== 'Director' &&
                            c.job !== 'Creator' &&
                            // Also exclude any crew member who is already shown as a director
                            !data.crew?.some(director =>
                              (director.job === 'Director' || director.job === 'Creator') &&
                              director.id === c.id
                            )
                          )
                          .slice(0, 4) // Limit to 4 additional crew members
                          .map((crewMember) => (
                            <div key={`${crewMember.id}-${crewMember.job}`} className="flex items-center bg-vista-dark/40 rounded-lg p-3 border border-vista-light/5 transition-all duration-300 hover:border-vista-blue/20 hover:bg-vista-dark/60 transform hover:scale-[1.02]">
                              <PersonImage
                                id={crewMember.id}
                                name={crewMember.name}
                                profilePath={crewMember.profile_path}
                                gender={crewMember.gender}
                                className="mr-3"
                                size="md"
                              />
                              <div>
                                <p className="text-vista-light font-medium text-sm leading-tight">{crewMember.name}</p>
                                <p className="text-vista-light/60 text-xs">{crewMember.job}</p>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </div>
          </div>

          {/* Related Content with Modern Animation - Mobile optimized */}
          {(data.similar?.length ?? 0) > 0 || (data.recommendations?.length ?? 0) > 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.2 }}
              className="mt-8 sm:mt-12"
            >
              <div className="flex items-center mb-6">
                <div className="h-8 w-1.5 bg-gradient-to-b from-vista-blue to-vista-blue/60 rounded-full mr-3 shadow-sm"></div>
                <h2 className="text-2xl font-bold">You May Also Like</h2>
              </div>
              <DetailRelatedContent
                contentId={data.id}
                type={data.type}
                similar={data.similar}
                recommendations={data.recommendations}
              />
            </motion.div>
          ) : null}
        </div>
      </div>



      {/* Simple Footer */}
      <SimpleFooter className="border-t border-vista-light/5 mt-8" />
    </div>
  );
}

/* Custom CSS for enhanced banner and improved mobile experience */
const styles = document.createElement('style');
styles.innerHTML = `
.text-shadow-lg {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}
.text-shadow-md {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

/* Enhanced radial gradient for banner vignette effect */
.bg-radial-gradient {
  background: radial-gradient(ellipse at center, transparent 0%, transparent 40%, rgba(0, 0, 0, 0.4) 70%, rgba(0, 0, 0, 0.8) 100%);
}

/* Smooth backdrop blur transition */
.backdrop-blur-xl {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Enhanced glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Animation delays for staggered effects */
.delay-300 { animation-delay: 300ms; }
.delay-700 { animation-delay: 700ms; }

/* Enhanced hover effects for metadata cards */
.metadata-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.metadata-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
}

/* Smooth color transitions */
.text-transition {
  transition: color 0.3s ease-in-out;
}

/* Enhanced button hover effects */
.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-glow:hover::before {
  left: 100%;
}

@media (max-width: 640px) {
  /* Improve trailer visibility on mobile */
  #trailer-container {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
    width: calc(100% + 1rem);
    border-radius: 0;
    padding-left: 0;
    padding-right: 0;
  }

  /* Adjust content spacing on mobile */
  .mobile-content-spacing {
    margin-bottom: 1rem;
  }

  /* Optimize banner height for mobile */
  .banner-mobile {
    min-height: 65vh;
  }

  /* Adjust metadata cards for mobile */
  .metadata-card {
    padding: 0.75rem;
  }
}

/* Extra small screen support */
@media (max-width: 375px) {
  /* Further adjustments for very small screens */
  #trailer-container {
    margin-left: -1rem;
    margin-right: -1rem;
    width: calc(100% + 2rem);
  }

  /* Typography and spacing utilities */
  .xs\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .xs\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .xs\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .xs\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .xs\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .xs\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

  .xs\:mb-2 { margin-bottom: 0.5rem; }
  .xs\:mb-4 { margin-bottom: 1rem; }
  .xs\:mb-6 { margin-bottom: 1.5rem; }

  .xs\:mt-2 { margin-top: 0.5rem; }

  .xs\:p-3 { padding: 0.75rem; }
  .xs\:p-4 { padding: 1rem; }

  .xs\:px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
  .xs\:px-4 { padding-left: 1rem; padding-right: 1rem; }

  .xs\:py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
  .xs\:py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }

  .xs\:gap-2 { gap: 0.5rem; }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Optimize banner for extra small screens */
  .banner-mobile {
    min-height: 60vh;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .glass-morphism {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-pulse,
  .animate-bounce,
  .transition-transform {
    animation: none !important;
    transition: none !important;
  }
}
`;
document.head.appendChild(styles);
